2025-08-09 15:14:13 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:14:13 | INFO     | __main__:<module>:358 | 🚀 正常开发模式，启用自动重载
2025-08-09 15:14:13 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:14:13 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-08-09 15:14:13 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: localhost:3306/test_case_automation
2025-08-09 15:14:13 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-09 15:14:13 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-08-09 15:14:13 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-08-09 15:14:13 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-09 15:14:13 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-09 15:14:13 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-08-09 15:14:13 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-08-09 15:16:22 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:16:23 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:14 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:17 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:19 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_cases:285 | 获取测试用例列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:21 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:17:30 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:22:55 | INFO     | main:lifespan:80 | 🔄 正在关闭系统...
2025-08-09 15:22:55 | INFO     | app.database.connection:close:127 | 数据库连接已关闭
2025-08-09 15:22:55 | INFO     | main:lifespan:85 | ✅ 数据库连接已关闭
2025-08-09 15:22:55 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-08-09 15:22:55 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
2025-08-09 15:22:55 | INFO     | main:lifespan:89 | ✅ 智能体资源已清理
2025-08-09 15:22:55 | INFO     | main:lifespan:91 | 👋 系统已安全关闭
2025-08-09 15:22:57 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:22:57 | INFO     | __main__:<module>:358 | 🚀 正常开发模式，启用自动重载
2025-08-09 15:22:57 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:22:57 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-08-09 15:22:57 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 106.54.164.19:3306/case_demo
2025-08-09 15:22:58 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-09 15:22:58 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-08-09 15:22:58 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-08-09 15:22:58 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-09 15:22:58 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-09 15:22:58 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-08-09 15:22:58 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:23:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:27:02 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:02 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:27:02 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:27:05 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:05 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:27:05 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:31:41 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:31:41 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:31:41 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:36:13 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:36:13 | INFO     | main:lifespan:80 | 🔄 正在关闭系统...
2025-08-09 15:36:13 | INFO     | app.database.connection:close:127 | 数据库连接已关闭
2025-08-09 15:36:13 | INFO     | main:lifespan:85 | ✅ 数据库连接已关闭
2025-08-09 15:36:13 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-08-09 15:36:13 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
2025-08-09 15:36:13 | INFO     | main:lifespan:89 | ✅ 智能体资源已清理
2025-08-09 15:36:13 | INFO     | main:lifespan:91 | 👋 系统已安全关闭
2025-08-09 15:36:35 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:36:35 | INFO     | __main__:<module>:358 | 🚀 正常开发模式，启用自动重载
2025-08-09 15:36:35 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:36:35 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-08-09 15:36:35 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 106.54.164.19:3306/case_demo
2025-08-09 15:36:35 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-09 15:36:35 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-08-09 15:36:35 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-08-09 15:36:35 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-09 15:36:35 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-09 15:36:35 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-08-09 15:36:35 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-08-09 15:36:42 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:36:42 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:36:42 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:37:35 | INFO     | main:lifespan:80 | 🔄 正在关闭系统...
2025-08-09 15:37:35 | INFO     | app.database.connection:close:127 | 数据库连接已关闭
2025-08-09 15:37:35 | INFO     | main:lifespan:85 | ✅ 数据库连接已关闭
2025-08-09 15:37:35 | INFO     | app.agents.factory:cleanup:433 | 清理智能体工厂资源...
2025-08-09 15:37:35 | INFO     | app.agents.factory:cleanup:435 | 智能体工厂资源清理完成
2025-08-09 15:37:35 | INFO     | main:lifespan:89 | ✅ 智能体资源已清理
2025-08-09 15:37:35 | INFO     | main:lifespan:91 | 👋 系统已安全关闭
2025-08-09 15:37:38 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:37:38 | INFO     | __main__:<module>:358 | 🚀 正常开发模式，启用自动重载
2025-08-09 15:37:38 | INFO     | app.core.logging:setup_logging:135 | 日志系统初始化完成
2025-08-09 15:37:38 | INFO     | main:lifespan:41 | 🚀 启动企业级测试用例生成系统...
2025-08-09 15:37:38 | INFO     | app.database.connection:initialize:37 | 使用配置的数据库URL: 106.54.164.19:3306/case_demo
2025-08-09 15:37:38 | INFO     | app.database.connection:initialize:70 | 数据库连接初始化成功
2025-08-09 15:37:38 | INFO     | main:lifespan:46 | ✅ 数据库连接初始化完成
2025-08-09 15:37:38 | INFO     | main:lifespan:64 | ✅ 上传目录创建完成
2025-08-09 15:37:38 | INFO     | app.agents.factory:initialize:423 | 智能体工厂初始化中...
2025-08-09 15:37:38 | INFO     | app.agents.factory:initialize:425 | 智能体工厂初始化完成
2025-08-09 15:37:38 | INFO     | main:lifespan:69 | ✅ 智能体工厂初始化完成
2025-08-09 15:37:38 | INFO     | main:lifespan:71 | 🎉 系统启动完成!
2025-08-09 15:37:43 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:37:43 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:37:43 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
