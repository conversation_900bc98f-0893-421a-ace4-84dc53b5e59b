#!/usr/bin/env python3
"""
数据库修复脚本：修复项目状态枚举值
将数据库中的小写状态值转换为大写状态值
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from loguru import logger
from sqlalchemy import text
from app.database.connection import db_manager
from app.core.enums import ProjectStatus


async def fix_project_status_values():
    """修复项目状态枚举值"""
    try:
        logger.info("开始修复项目状态枚举值...")
        
        # 初始化数据库连接
        await db_manager.initialize()
        
        async with db_manager.get_session() as session:
            # 1. 检查当前状态值
            logger.info("检查当前项目状态值...")
            result = await session.execute(text("SELECT DISTINCT status FROM projects"))
            current_statuses = [row[0] for row in result.fetchall()]
            logger.info(f"当前状态值: {current_statuses}")
            
            # 2. 统计需要修复的记录
            count_result = await session.execute(text("""
                SELECT status, COUNT(*) as count 
                FROM projects 
                WHERE status NOT IN ('ACTIVE', 'ARCHIVED')
                GROUP BY status
            """))
            invalid_statuses = count_result.fetchall()
            
            if not invalid_statuses:
                logger.info("没有发现需要修复的项目状态值")
                return True
            
            logger.info("发现需要修复的状态值:")
            total_to_fix = 0
            for status, count in invalid_statuses:
                logger.info(f"  - '{status}': {count} 条记录")
                total_to_fix += count
            
            # 3. 修复状态值
            logger.info(f"开始修复 {total_to_fix} 条记录...")
            
            # 修复 'active' -> 'ACTIVE'
            active_result = await session.execute(text("""
                UPDATE projects 
                SET status = 'ACTIVE' 
                WHERE status IN ('active', 'Active')
            """))
            logger.info(f"修复 active 状态: {active_result.rowcount} 条记录")
            
            # 修复 'archived' -> 'ARCHIVED'
            archived_result = await session.execute(text("""
                UPDATE projects 
                SET status = 'ARCHIVED' 
                WHERE status IN ('archived', 'Archived')
            """))
            logger.info(f"修复 archived 状态: {archived_result.rowcount} 条记录")
            
            # 修复其他无效状态为默认值 'ACTIVE'
            other_result = await session.execute(text("""
                UPDATE projects 
                SET status = 'ACTIVE' 
                WHERE status NOT IN ('ACTIVE', 'ARCHIVED')
            """))
            logger.info(f"修复其他无效状态: {other_result.rowcount} 条记录")
            
            # 4. 验证修复结果
            logger.info("验证修复结果...")
            final_result = await session.execute(text("SELECT DISTINCT status FROM projects"))
            final_statuses = [row[0] for row in final_result.fetchall()]
            logger.info(f"修复后状态值: {final_statuses}")
            
            # 检查是否还有无效状态
            invalid_final = [s for s in final_statuses if s not in ['ACTIVE', 'ARCHIVED']]
            if invalid_final:
                logger.warning(f"仍有无效状态值: {invalid_final}")
                return False
            
            logger.success("项目状态枚举值修复完成!")
            return True
            
    except Exception as e:
        logger.error(f"修复项目状态枚举值失败: {str(e)}")
        return False


async def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("项目状态枚举值修复脚本")
    logger.info("=" * 50)
    
    success = await fix_project_status_values()
    
    if success:
        logger.success("修复完成!")
        return 0
    else:
        logger.error("修复失败!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
