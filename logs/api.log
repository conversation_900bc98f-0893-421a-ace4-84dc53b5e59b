2025-08-09 15:16:22 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:16:23 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:14 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:17 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:19 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_cases:285 | 获取测试用例列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:21 | ERROR    | app.api.v1.endpoints.test_case_management:get_test_case_stats:349 | 获取测试用例统计失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:17:26 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:17:30 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.OperationalError) (2003, "Can't connect to MySQL server on 'localhost'")
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:23:07 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:23:07 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:24 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:24:24 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.projects' doesn't exist")
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:24:26 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:24:26 | ERROR    | app.api.v1.endpoints.test_case_generator:list_sessions:1201 | 获取会话列表失败: (pymysql.err.ProgrammingError) (1146, "Table 'case_demo.processing_sessions' doesn't exist")
[SQL: 
                    SELECT id, session_type, status, progress, agent_type,
                           processing_time, error_message, generated_count,
                           started_at, completed_at, created_at, updated_at
                    FROM processing_sessions
                    ORDER BY created_at DESC
                    LIMIT 50
                ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-08-09 15:27:02 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:02 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:27:02 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:27:05 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:27:05 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:27:05 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:31:41 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:31:41 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:31:41 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
2025-08-09 15:36:13 | ERROR    | app.api.v1.endpoints.projects:get_projects:192 | 获取项目列表失败: (pymysql.err.OperationalError) (2013, 'Lost connection to MySQL server during query ([WinError 10054] 远程主机强迫关闭了一个现有的连接。)')
[SQL: SELECT count(*) AS count_1 
FROM (SELECT projects.id AS id, projects.name AS name, projects.description AS description, projects.status AS status, projects.created_at AS created_at, projects.updated_at AS updated_at 
FROM projects 
WHERE projects.status = %s ORDER BY projects.updated_at DESC) AS anon_1]
[parameters: ('ACTIVE',)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-08-09 15:36:42 | ERROR    | app.api.v1.endpoints.projects:get_projects:180 | 获取项目列表失败: 'active' is not among the defined enum values. Enum name: projectstatus. Possible values: ACTIVE, ARCHIVED
2025-08-09 15:36:42 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1134 | 内存中的活跃会话数量: 0
2025-08-09 15:36:42 | INFO     | app.api.v1.endpoints.test_case_generator:list_sessions:1181 | 从数据库查询到 0 个历史会话
